/**
 * Utilities for generating Remix meta functions with enhanced SEO
 */

import type { MetaFunction } from "@remix-run/cloudflare";
import {
  getCanonicalUrl as getBaseCanonicalUrl,
  getPageConfig,
  type pageConfigs,
  siteConfig,
} from "~/config/seo";
import { generateSEOTags, generateTitle, type SEOConfig, seoTagsToRemixMeta } from "./seo";

export interface EnhancedMetaConfig extends Partial<SEOConfig> {
  pageKey?: keyof typeof pageConfigs;
  pathname?: string;
  structuredData?: object;
}

/**
 * Create an enhanced meta function with SEO optimization
 */
export function createMetaFunction(config: EnhancedMetaConfig): MetaFunction {
  return ({ location }) => {
    const pathname = location.pathname;

    // Get base configuration
    let baseConfig: any = {};
    if (config.pageKey) {
      // Simplified: directly get page config without localization
      baseConfig = getPageConfig(config.pageKey) || {};
    }

    // Merge configurations
    const canonicalUrl = getBaseCanonicalUrl(pathname); // Simplified canonical URL
    const finalConfig: SEOConfig = {
      ...baseConfig,
      ...config,
      siteName: siteConfig.name,
      url: canonicalUrl,
      canonical: canonicalUrl,
      image: config.image || `${siteConfig.url}${siteConfig.ogImage}`,
      author: config.author || siteConfig.author,
    };

    // Generate SEO tags
    const seoTags = generateSEOTags(finalConfig);
    const metaTags = seoTagsToRemixMeta(seoTags);

    // Add title
    const title = generateTitle(
      finalConfig.title || baseConfig.title || siteConfig.name,
      undefined,
      siteConfig.name
    );

    const result = [{ title }, ...metaTags];

    // Add structured data if provided
    if (config.structuredData) {
      result.push({
        "script:ld+json": config.structuredData,
      });
    }

    return result;
  };
}

/**
 * Quick meta function for simple pages
 */
export function createSimpleMeta(
  title: string,
  description: string,
  options: {
    keywords?: string;
    noIndex?: boolean;
    image?: string;
  } = {}
): MetaFunction {
  return createMetaFunction({
    title,
    description,
    keywords: options.keywords,
    noIndex: options.noIndex,
    image: options.image,
  });
}

/**
 * Meta function for localized pages using translation keys
 * This version works with Remix loaders to get translations
 */
export function createLocalizedMeta(pageKey: keyof typeof pageConfigs): MetaFunction {
  return ({ data, location }) => {
    const pathname = location.pathname;

    // Get base configuration
    // Simplified: directly get page config without localization or t function
    const baseConfig = getPageConfig(pageKey) || {};

    // Merge configurations
    const canonicalUrl = getBaseCanonicalUrl(pathname); // Simplified canonical URL
    const finalConfig: SEOConfig = {
      ...baseConfig,
      siteName: siteConfig.name,
      url: canonicalUrl,
      canonical: canonicalUrl,
      image: `${siteConfig.url}${siteConfig.ogImage}`,
      author: siteConfig.author,
    };

    // Generate SEO tags
    const seoTags = generateSEOTags(finalConfig);
    const metaTags = seoTagsToRemixMeta(seoTags);

    // Removed language-specific meta tags

    // Add title
    const title = generateTitle(
      finalConfig.title || baseConfig.title || siteConfig.name,
      undefined,
      siteConfig.name
    );

    const result = [
      { title },
      ...metaTags,
      // ...languageTags, // Removed languageTags
    ];

    // Add structured data if provided
    const structuredData = (data as any)?.structuredData;
    if (structuredData) {
      result.push({
        "script:ld+json": structuredData,
      });
    }

    return result;
  };
}

/**
 * Generate canonical link for the Layout component
 */
export function generateCanonicalLink(pathname: string) {
  return {
    rel: "canonical",
    href: getBaseCanonicalUrl(pathname), // Use simplified canonical URL
  };
}

/**
 * Generate page title with site name
 */
export function getPageTitle(title: string, siteName?: string): string {
  const site = siteName || siteConfig.name;
  return title ? `${title} | ${site}` : site;
}
