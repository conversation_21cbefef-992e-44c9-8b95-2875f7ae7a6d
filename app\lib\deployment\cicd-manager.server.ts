/**
 * CI/CD Pipeline Management System
 * Manages continuous integration and deployment pipelines
 */

export interface Pipeline {
  id: string;
  name: string;
  description: string;
  repository: string;
  branch: string;
  trigger: "push" | "pull_request" | "manual" | "scheduled";
  environment: string;
  status: "idle" | "running" | "success" | "failed" | "cancelled";
  stages: PipelineStage[];
  lastRun?: PipelineRun;
  createdAt: Date;
  updatedAt: Date;
  enabled: boolean;
}

export interface PipelineStage {
  id: string;
  name: string;
  type: "build" | "test" | "security" | "deploy" | "notify";
  commands: string[];
  environment?: Record<string, string>;
  timeout: number; // seconds
  retryCount: number;
  continueOnError: boolean;
  dependsOn?: string[];
}

export interface PipelineRun {
  id: string;
  pipelineId: string;
  status: "pending" | "running" | "success" | "failed" | "cancelled";
  trigger: string;
  branch: string;
  commit: string;
  commitMessage: string;
  author: string;
  startTime: Date;
  endTime?: Date;
  duration?: number;
  stageRuns: StageRun[];
  logs: string[];
  artifacts?: Artifact[];
}

export interface StageRun {
  stageId: string;
  stageName: string;
  status: "pending" | "running" | "success" | "failed" | "skipped" | "cancelled";
  startTime?: Date;
  endTime?: Date;
  duration?: number;
  logs: string[];
  exitCode?: number;
  retryAttempt: number;
}

export interface Artifact {
  id: string;
  name: string;
  type: "build" | "test-results" | "coverage" | "logs" | "deployment";
  size: number;
  path: string;
  downloadUrl: string;
  expiresAt: Date;
}

export interface PipelineMetrics {
  totalRuns: number;
  successfulRuns: number;
  failedRuns: number;
  successRate: number;
  averageDuration: number;
  runsToday: number;
  runsThisWeek: number;
  recentRuns: PipelineRun[];
  runsByStatus: Record<string, number>;
  runTrends: Array<{
    date: Date;
    runs: number;
    successRate: number;
    avgDuration: number;
  }>;
}

// Default pipeline templates
const pipelineTemplates = {
  "remix-cloudflare": {
    name: "Remix + Cloudflare Workers",
    description: "Standard CI/CD pipeline for Remix applications on Cloudflare Workers",
    stages: [
      {
        id: "install",
        name: "Install Dependencies",
        type: "build" as const,
        commands: ["pnpm install --frozen-lockfile", "pnpm run typegen"],
        timeout: 300,
        retryCount: 2,
        continueOnError: false,
      },
      {
        id: "lint",
        name: "Lint & Format",
        type: "test" as const,
        commands: ["pnpm run check", "pnpm run typecheck"],
        timeout: 180,
        retryCount: 1,
        continueOnError: false,
        dependsOn: ["install"],
      },
      {
        id: "test",
        name: "Run Tests",
        type: "test" as const,
        commands: ["pnpm run test"],
        timeout: 300,
        retryCount: 1,
        continueOnError: false,
        dependsOn: ["install"],
      },
      {
        id: "security",
        name: "Security Scan",
        type: "security" as const,
        commands: ["pnpm audit --audit-level moderate", 'echo "Security scan completed"'],
        timeout: 120,
        retryCount: 1,
        continueOnError: true,
        dependsOn: ["install"],
      },
      {
        id: "build",
        name: "Build Application",
        type: "build" as const,
        commands: ["pnpm run build"],
        timeout: 300,
        retryCount: 1,
        continueOnError: false,
        dependsOn: ["lint", "test"],
      },
      {
        id: "deploy",
        name: "Deploy to Cloudflare",
        type: "deploy" as const,
        commands: ["wrangler deploy"],
        timeout: 180,
        retryCount: 2,
        continueOnError: false,
        dependsOn: ["build"],
      },
    ],
  },
};

// In-memory storage
const pipelines: Pipeline[] = [];
const pipelineRuns: PipelineRun[] = [];

/**
 * Create a new pipeline
 */
export function createPipeline(
  name: string,
  repository: string,
  branch: string,
  environment: string,
  template = "remix-cloudflare"
): string {
  const templateConfig = pipelineTemplates[template as keyof typeof pipelineTemplates];
  if (!templateConfig) {
    throw new Error(`Template ${template} not found`);
  }

  const pipeline: Pipeline = {
    id: generatePipelineId(),
    name,
    description: templateConfig.description,
    repository,
    branch,
    trigger: "push",
    environment,
    status: "idle",
    stages: templateConfig.stages,
    createdAt: new Date(),
    updatedAt: new Date(),
    enabled: true,
  };

  pipelines.push(pipeline);
  console.log(`[CICD] Created pipeline ${pipeline.id}: ${name}`);

  return pipeline.id;
}

/**
 * Trigger pipeline run
 */
export async function triggerPipelineRun(
  pipelineId: string,
  trigger: string,
  commit: string,
  commitMessage: string,
  author: string
): Promise<string> {
  const pipeline = pipelines.find((p) => p.id === pipelineId);
  if (!pipeline || !pipeline.enabled) {
    throw new Error("Pipeline not found or disabled");
  }

  const run: PipelineRun = {
    id: generateRunId(),
    pipelineId,
    status: "pending",
    trigger,
    branch: pipeline.branch,
    commit,
    commitMessage,
    author,
    startTime: new Date(),
    stageRuns: pipeline.stages.map((stage) => ({
      stageId: stage.id,
      stageName: stage.name,
      status: "pending",
      logs: [],
      retryAttempt: 0,
    })),
    logs: [],
  };

  pipelineRuns.push(run);
  pipeline.lastRun = run;
  pipeline.status = "running";

  // Start pipeline execution
  executePipelineRun(run.id);

  console.log(`[CICD] Triggered pipeline run ${run.id} for ${pipeline.name}`);
  return run.id;
}

/**
 * Execute pipeline run
 */
async function executePipelineRun(runId: string): Promise<void> {
  const run = pipelineRuns.find((r) => r.id === runId);
  const pipeline = pipelines.find((p) => p.id === run?.pipelineId);

  if (!run || !pipeline) return;

  try {
    run.status = "running";
    run.logs.push(`[${new Date().toISOString()}] Starting pipeline execution`);

    // Execute stages in dependency order
    const executedStages = new Set<string>();

    while (executedStages.size < pipeline.stages.length) {
      const readyStages = pipeline.stages.filter((stage) => {
        if (executedStages.has(stage.id)) return false;

        // Check if all dependencies are completed
        if (stage.dependsOn) {
          return stage.dependsOn.every((dep) => executedStages.has(dep));
        }

        return true;
      });

      if (readyStages.length === 0) {
        throw new Error("Circular dependency detected in pipeline stages");
      }

      // Execute ready stages in parallel
      await Promise.all(
        readyStages.map(async (stage) => {
          await executeStage(run, stage);
          executedStages.add(stage.id);
        })
      );

      // Check if any stage failed and should stop the pipeline
      const failedStages = run.stageRuns.filter(
        (sr) =>
          sr.status === "failed" &&
          !pipeline.stages.find((s) => s.id === sr.stageId)?.continueOnError
      );

      if (failedStages.length > 0) {
        throw new Error(`Pipeline failed at stage: ${failedStages[0].stageName}`);
      }
    }

    // Pipeline completed successfully
    run.status = "success";
    run.endTime = new Date();
    run.duration = run.endTime.getTime() - run.startTime.getTime();
    run.logs.push(`[${new Date().toISOString()}] Pipeline completed successfully`);

    pipeline.status = "success";
  } catch (error) {
    run.status = "failed";
    run.endTime = new Date();
    run.duration = run.endTime.getTime() - run.startTime.getTime();
    run.logs.push(`[${new Date().toISOString()}] Pipeline failed: ${error}`);

    pipeline.status = "failed";

    // Cancel remaining stages
    run.stageRuns.forEach((stageRun) => {
      if (stageRun.status === "pending") {
        stageRun.status = "cancelled";
      }
    });

    console.error(`[CICD] Pipeline run ${runId} failed:`, error);
  }
}

/**
 * Execute a single stage
 */
async function executeStage(run: PipelineRun, stage: PipelineStage): Promise<void> {
  const stageRun = run.stageRuns.find((sr) => sr.stageId === stage.id);
  if (!stageRun) return;

  let attempt = 0;
  let success = false;

  while (attempt <= stage.retryCount && !success) {
    try {
      stageRun.status = "running";
      stageRun.startTime = new Date();
      stageRun.retryAttempt = attempt;

      if (attempt > 0) {
        stageRun.logs.push(`[${new Date().toISOString()}] Retrying stage (attempt ${attempt + 1})`);
      }

      stageRun.logs.push(`[${new Date().toISOString()}] Starting stage: ${stage.name}`);

      // Execute commands
      for (const command of stage.commands) {
        stageRun.logs.push(`[${new Date().toISOString()}] Executing: ${command}`);

        // Simulate command execution
        await new Promise((resolve) => setTimeout(resolve, Math.random() * 2000 + 1000));

        // Simulate occasional failures for testing
        if (Math.random() < 0.1 && attempt === 0) {
          throw new Error(`Command failed: ${command}`);
        }

        stageRun.logs.push(`[${new Date().toISOString()}] Command completed successfully`);
      }

      stageRun.status = "success";
      stageRun.endTime = new Date();
      stageRun.duration = stageRun.endTime.getTime() - stageRun.startTime!.getTime();
      stageRun.exitCode = 0;
      stageRun.logs.push(`[${new Date().toISOString()}] Stage completed successfully`);

      success = true;
    } catch (error) {
      attempt++;
      stageRun.logs.push(`[${new Date().toISOString()}] Stage failed: ${error}`);

      if (attempt > stage.retryCount) {
        stageRun.status = "failed";
        stageRun.endTime = new Date();
        stageRun.duration = stageRun.endTime.getTime() - stageRun.startTime!.getTime();
        stageRun.exitCode = 1;

        if (!stage.continueOnError) {
          throw error;
        }
      } else {
        // Wait before retry
        await new Promise((resolve) => setTimeout(resolve, 1000 * attempt));
      }
    }
  }
}

/**
 * Get pipeline by ID
 */
export function getPipeline(pipelineId: string): Pipeline | null {
  return pipelines.find((p) => p.id === pipelineId) || null;
}

/**
 * Get all pipelines
 */
export function getPipelines(): Pipeline[] {
  return [...pipelines];
}

/**
 * Get pipeline runs
 */
export function getPipelineRuns(pipelineId?: string, limit = 50): PipelineRun[] {
  let filtered = [...pipelineRuns];

  if (pipelineId) {
    filtered = filtered.filter((run) => run.pipelineId === pipelineId);
  }

  return filtered.sort((a, b) => b.startTime.getTime() - a.startTime.getTime()).slice(0, limit);
}

/**
 * Get pipeline run by ID
 */
export function getPipelineRun(runId: string): PipelineRun | null {
  return pipelineRuns.find((run) => run.id === runId) || null;
}

/**
 * Cancel pipeline run
 */
export function cancelPipelineRun(runId: string): boolean {
  const run = pipelineRuns.find((r) => r.id === runId);
  const pipeline = pipelines.find((p) => p.id === run?.pipelineId);

  if (!run || !pipeline || run.status !== "running") {
    return false;
  }

  run.status = "cancelled";
  run.endTime = new Date();
  run.duration = run.endTime.getTime() - run.startTime.getTime();
  run.logs.push(`[${new Date().toISOString()}] Pipeline cancelled by user`);

  // Cancel running stages
  run.stageRuns.forEach((stageRun) => {
    if (stageRun.status === "running" || stageRun.status === "pending") {
      stageRun.status = "cancelled";
    }
  });

  pipeline.status = "idle";

  console.log(`[CICD] Cancelled pipeline run ${runId}`);
  return true;
}

/**
 * Get pipeline metrics
 */
export function getPipelineMetrics(pipelineId?: string): PipelineMetrics {
  let runs = [...pipelineRuns];

  if (pipelineId) {
    runs = runs.filter((run) => run.pipelineId === pipelineId);
  }

  const totalRuns = runs.length;
  const successfulRuns = runs.filter((run) => run.status === "success").length;
  const failedRuns = runs.filter((run) => run.status === "failed").length;
  const successRate = totalRuns > 0 ? (successfulRuns / totalRuns) * 100 : 0;

  const completedRuns = runs.filter((run) => run.duration);
  const averageDuration =
    completedRuns.length > 0
      ? completedRuns.reduce((sum, run) => sum + (run.duration || 0), 0) / completedRuns.length
      : 0;

  const now = new Date();
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
  const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

  const runsToday = runs.filter((run) => run.startTime >= today).length;
  const runsThisWeek = runs.filter((run) => run.startTime >= weekAgo).length;

  // Count by status
  const runsByStatus: Record<string, number> = {};
  runs.forEach((run) => {
    runsByStatus[run.status] = (runsByStatus[run.status] || 0) + 1;
  });

  // Generate trends (last 7 days)
  const runTrends = Array.from({ length: 7 }, (_, i) => {
    const date = new Date(today.getTime() - i * 24 * 60 * 60 * 1000);
    const dayStart = new Date(date.getFullYear(), date.getMonth(), date.getDate());
    const dayEnd = new Date(dayStart.getTime() + 24 * 60 * 60 * 1000);

    const dayRuns = runs.filter((run) => run.startTime >= dayStart && run.startTime < dayEnd);

    const daySuccessful = dayRuns.filter((run) => run.status === "success").length;
    const daySuccessRate = dayRuns.length > 0 ? (daySuccessful / dayRuns.length) * 100 : 0;

    const dayCompleted = dayRuns.filter((run) => run.duration);
    const dayAvgDuration =
      dayCompleted.length > 0
        ? dayCompleted.reduce((sum, run) => sum + (run.duration || 0), 0) / dayCompleted.length
        : 0;

    return {
      date,
      runs: dayRuns.length,
      successRate: daySuccessRate,
      avgDuration: dayAvgDuration,
    };
  }).reverse();

  return {
    totalRuns,
    successfulRuns,
    failedRuns,
    successRate,
    averageDuration,
    runsToday,
    runsThisWeek,
    recentRuns: runs.slice(0, 10),
    runsByStatus,
    runTrends,
  };
}

/**
 * Update pipeline configuration
 */
export function updatePipeline(pipelineId: string, updates: Partial<Pipeline>): boolean {
  const index = pipelines.findIndex((p) => p.id === pipelineId);
  if (index === -1) return false;

  pipelines[index] = {
    ...pipelines[index],
    ...updates,
    updatedAt: new Date(),
  };

  console.log(`[CICD] Updated pipeline ${pipelineId}`);
  return true;
}

/**
 * Delete pipeline
 */
export function deletePipeline(pipelineId: string): boolean {
  const index = pipelines.findIndex((p) => p.id === pipelineId);
  if (index === -1) return false;

  pipelines.splice(index, 1);

  // Also remove associated runs
  const runIndices = pipelineRuns
    .map((run, i) => (run.pipelineId === pipelineId ? i : -1))
    .filter((i) => i !== -1)
    .reverse();

  runIndices.forEach((i) => pipelineRuns.splice(i, 1));

  console.log(`[CICD] Deleted pipeline ${pipelineId}`);
  return true;
}

/**
 * Helper functions
 */
function generatePipelineId(): string {
  return `pipeline_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

function generateRunId(): string {
  return `run_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

/**
 * Initialize default pipelines
 */
export function initializeDefaultPipelines(): void {
  // Create default pipelines for each environment
  const environments = ["development", "staging", "production"];

  environments.forEach((env) => {
    createPipeline(
      `${env.charAt(0).toUpperCase() + env.slice(1)} Pipeline`,
      "remix-cloudflare-neon-starter",
      env === "development" ? "develop" : "main",
      env
    );
  });

  console.log("[CICD] Initialized default pipelines");
}

// Note: Initialization moved to be called explicitly when needed
// to avoid global scope issues in Cloudflare Workers
// Call initializeDefaultPipelines() explicitly in your application startup
