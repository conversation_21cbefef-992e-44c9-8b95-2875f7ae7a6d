// Billing, subscription, and payment related schemas
import { relations } from "drizzle-orm";
import {
  boolean,
  decimal,
  index,
  integer,
  jsonb,
  pgEnum,
  pgTable,
  uuid as pgUuid,
  serial,
  text,
  timestamp,
  varchar,
} from "drizzle-orm/pg-core";
import { users } from "./users";

// Billing enums
export const billingProviderEnum = pgEnum("billing_provider", [
  "stripe",
  "lemon-squeezy",
  "paddle",
]);

export const subscriptionStatusEnum = pgEnum("subscription_status", [
  "active",
  "trialing",
  "past_due",
  "canceled",
  "unpaid",
  "incomplete",
  "incomplete_expired",
  "paused",
]);

export const paymentStatusEnum = pgEnum("payment_status", ["pending", "succeeded", "failed"]);

// Affiliates table
export const affiliates = pgTable(
  "affiliates",
  {
    id: pgUuid("id").primaryKey().defaultRandom(),
    userId: pgUuid("user_id")
      .notNull()
      .references(() => users.id, { onDelete: "cascade" }),
    code: varchar("code", { length: 50 }).notNull().unique(),
    commissionRate: decimal("commission_rate", { precision: 5, scale: 4 })
      .notNull()
      .default("0.1000"), // 10%
    totalEarnings: decimal("total_earnings", { precision: 10, scale: 2 }).notNull().default("0.00"),
    isActive: boolean("is_active").default(true).notNull(),
    createdAt: timestamp("created_at").defaultNow().notNull(),
    updatedAt: timestamp("updated_at")
      .defaultNow()
      .notNull()
      .$onUpdate(() => new Date()),
  },
  (table) => ({
    userIdx: index("affiliates_user_idx").on(table.userId),
    codeIdx: index("affiliates_code_idx").on(table.code),
    activeIdx: index("affiliates_active_idx").on(table.isActive),
  })
);

// Billing customers table
export const billingCustomers = pgTable(
  "billing_customers",
  {
    id: pgUuid("id").primaryKey().defaultRandom(),
    userId: pgUuid("user_id")
      .notNull()
      .references(() => users.id, { onDelete: "cascade" }),
    provider: billingProviderEnum("provider").notNull(),
    customerId: varchar("customer_id", { length: 255 }).notNull(),
    email: varchar("email", { length: 255 }).notNull(),
    name: varchar("name", { length: 255 }),
    createdAt: timestamp("created_at").defaultNow().notNull(),
    updatedAt: timestamp("updated_at")
      .defaultNow()
      .notNull()
      .$onUpdate(() => new Date()),
  },
  (table) => ({
    userIdx: index("billing_customers_user_idx").on(table.userId),
    providerCustomerIdx: index("billing_customers_provider_customer_idx").on(
      table.provider,
      table.customerId
    ),
  })
);

// Orders table
export const orders = pgTable(
  "orders",
  {
    id: pgUuid("id").primaryKey().defaultRandom(),
    userId: pgUuid("user_id")
      .notNull()
      .references(() => users.id, { onDelete: "cascade" }),
    affiliateId: pgUuid("affiliate_id").references(() => affiliates.id),
    orderNumber: varchar("order_number", { length: 100 }).notNull().unique(),
    status: paymentStatusEnum("status").notNull().default("pending"),
    subtotal: decimal("subtotal", { precision: 10, scale: 2 }).notNull(),
    tax: decimal("tax", { precision: 10, scale: 2 }).notNull().default("0.00"),
    total: decimal("total", { precision: 10, scale: 2 }).notNull(),
    currency: varchar("currency", { length: 3 }).notNull().default("USD"),
    provider: billingProviderEnum("provider").notNull(),
    providerOrderId: varchar("provider_order_id", { length: 255 }),
    metadata: jsonb("metadata"), // Additional order data
    createdAt: timestamp("created_at").defaultNow().notNull(),
    updatedAt: timestamp("updated_at")
      .defaultNow()
      .notNull()
      .$onUpdate(() => new Date()),
  },
  (table) => ({
    userIdx: index("orders_user_idx").on(table.userId),
    statusIdx: index("orders_status_idx").on(table.status),
    affiliateIdx: index("orders_affiliate_idx").on(table.affiliateId),
    orderNumberIdx: index("orders_number_idx").on(table.orderNumber),
    providerIdx: index("orders_provider_idx").on(table.provider),
  })
);

// Order items table
export const orderItems = pgTable(
  "order_items",
  {
    id: serial("id").primaryKey(),
    orderId: pgUuid("order_id")
      .notNull()
      .references(() => orders.id, { onDelete: "cascade" }),
    productId: varchar("product_id", { length: 255 }).notNull(),
    priceId: varchar("price_id", { length: 255 }).notNull(),
    quantity: integer("quantity").notNull().default(1),
    unitPrice: decimal("unit_price", { precision: 10, scale: 2 }).notNull(),
    totalPrice: decimal("total_price", { precision: 10, scale: 2 }).notNull(),
    metadata: jsonb("metadata"), // Product metadata
    createdAt: timestamp("created_at").defaultNow().notNull(),
    updatedAt: timestamp("updated_at")
      .defaultNow()
      .notNull()
      .$onUpdate(() => new Date()),
  },
  (table) => ({
    orderIdx: index("order_items_order_idx").on(table.orderId),
    productIdx: index("order_items_product_idx").on(table.productId),
  })
);

// Subscriptions table
export const subscriptions = pgTable(
  "subscriptions",
  {
    id: pgUuid("id").primaryKey().defaultRandom(),
    userId: pgUuid("user_id")
      .notNull()
      .references(() => users.id, { onDelete: "cascade" }),
    provider: billingProviderEnum("provider").notNull(),
    subscriptionId: varchar("subscription_id", { length: 255 }).notNull(),
    customerId: varchar("customer_id", { length: 255 }).notNull(),
    status: subscriptionStatusEnum("status").notNull(),
    currentPeriodStart: timestamp("current_period_start").notNull(),
    currentPeriodEnd: timestamp("current_period_end").notNull(),
    cancelAtPeriodEnd: boolean("cancel_at_period_end").default(false).notNull(),
    canceledAt: timestamp("canceled_at"),
    trialStart: timestamp("trial_start"),
    trialEnd: timestamp("trial_end"),
    metadata: jsonb("metadata"), // Additional subscription data
    createdAt: timestamp("created_at").defaultNow().notNull(),
    updatedAt: timestamp("updated_at")
      .defaultNow()
      .notNull()
      .$onUpdate(() => new Date()),
  },
  (table) => ({
    userIdx: index("subscriptions_user_idx").on(table.userId),
    subscriptionIdx: index("subscriptions_subscription_idx").on(table.subscriptionId),
    statusIdx: index("subscriptions_status_idx").on(table.status),
    periodEndIdx: index("subscriptions_period_end_idx").on(table.currentPeriodEnd),
  })
);

// Subscription items table
export const subscriptionItems = pgTable(
  "subscription_items",
  {
    id: pgUuid("id").primaryKey().defaultRandom(),
    subscriptionId: pgUuid("subscription_id")
      .notNull()
      .references(() => subscriptions.id, { onDelete: "cascade" }),
    provider: billingProviderEnum("provider").notNull(),
    subscriptionItemId: varchar("subscription_item_id", { length: 255 }).notNull(),
    priceId: varchar("price_id", { length: 255 }).notNull(),
    productId: varchar("product_id", { length: 255 }).notNull(),
    quantity: integer("quantity").notNull().default(1),
    metadata: jsonb("metadata"), // Additional item data
    createdAt: timestamp("created_at").defaultNow().notNull(),
    updatedAt: timestamp("updated_at")
      .defaultNow()
      .notNull()
      .$onUpdate(() => new Date()),
  },
  (table) => ({
    subscriptionIdx: index("subscription_items_subscription_idx").on(table.subscriptionId),
    itemIdx: index("subscription_items_item_idx").on(table.subscriptionItemId),
    priceIdx: index("subscription_items_price_idx").on(table.priceId),
    productIdx: index("subscription_items_product_idx").on(table.productId),
  })
);

// Credit transactions table
export const creditTransactions = pgTable(
  "credit_transactions",
  {
    id: pgUuid("id").primaryKey().defaultRandom(),
    userId: pgUuid("user_id")
      .notNull()
      .references(() => users.id, { onDelete: "cascade" }),
    amount: integer("amount").notNull(), // Can be negative for debits
    type: varchar("type", { length: 50 }).notNull(), // "purchase", "bonus", "usage", "refund"
    description: text("description"),
    orderId: pgUuid("order_id").references(() => orders.id),
    metadata: jsonb("metadata"), // Additional transaction data
    createdAt: timestamp("created_at").defaultNow().notNull(),
  },
  (table) => ({
    userIdx: index("credit_transactions_user_idx").on(table.userId),
    typeIdx: index("credit_transactions_type_idx").on(table.type),
    orderIdx: index("credit_transactions_order_idx").on(table.orderId),
    createdIdx: index("credit_transactions_created_idx").on(table.createdAt),
  })
);

// Relations
export const affiliatesRelations = relations(affiliates, ({ one, many }) => ({
  user: one(users, {
    fields: [affiliates.userId],
    references: [users.id],
  }),
  orders: many(orders),
}));

export const billingCustomersRelations = relations(billingCustomers, ({ one }) => ({
  user: one(users, {
    fields: [billingCustomers.userId],
    references: [users.id],
  }),
}));

export const ordersRelations = relations(orders, ({ one, many }) => ({
  user: one(users, {
    fields: [orders.userId],
    references: [users.id],
  }),
  affiliate: one(affiliates, {
    fields: [orders.affiliateId],
    references: [affiliates.id],
  }),
  items: many(orderItems),
  creditTransactions: many(creditTransactions),
}));

export const orderItemsRelations = relations(orderItems, ({ one }) => ({
  order: one(orders, {
    fields: [orderItems.orderId],
    references: [orders.id],
  }),
}));

export const subscriptionsRelations = relations(subscriptions, ({ one, many }) => ({
  user: one(users, {
    fields: [subscriptions.userId],
    references: [users.id],
  }),
  items: many(subscriptionItems),
}));

export const subscriptionItemsRelations = relations(subscriptionItems, ({ one }) => ({
  subscription: one(subscriptions, {
    fields: [subscriptionItems.subscriptionId],
    references: [subscriptions.id],
  }),
}));

export const creditTransactionsRelations = relations(creditTransactions, ({ one }) => ({
  user: one(users, {
    fields: [creditTransactions.userId],
    references: [users.id],
  }),
  order: one(orders, {
    fields: [creditTransactions.orderId],
    references: [orders.id],
  }),
}));
