/**
 * Subscription Management Page
 * Allows users to view, upgrade, downgrade, and cancel subscriptions
 */

import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/cloudflare";
import { json } from "@remix-run/cloudflare";
import { Form, Link, useActionData, useLoaderData, useNavigation } from "@remix-run/react";
import {
  AlertTriangle,
  ArrowDownRight,
  ArrowUpRight,
  Calendar,
  CheckCircle,
  CreditCard,
  Crown,
  RefreshCw,
  TrendingUp,
  XCircle,
  Zap,
} from "lucide-react";
import { useState } from "react";
import { Badge } from "~/components/ui/badge";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "~/components/ui/card";
import { Separator } from "~/components/ui/separator";
import { createDbFromEnv } from "~/lib/db";
import {
  calculateProration,
  cancelSubscription,
  createSubscriptionCheckout,
  getSubscriptionPlans,
  getUserSubscription,
  reactivateSubscription,
} from "~/services/subscription.server";
import { getUserUuid } from "~/services/user-management.server";

export async function loader({ request, context }: LoaderFunctionArgs) {
  try {
    const db = context.db || createDbFromEnv(context.cloudflare?.env);

    const userUuid = await getUserUuid();
    if (!userUuid) {
      return json({ success: false, error: "Not authenticated" }, { status: 401 });
    }

    const [subscription, plans] = await Promise.all([
      getUserSubscription(userUuid, db),
      Promise.resolve(getSubscriptionPlans()),
    ]);

    return json({
      success: true,
      data: {
        subscription,
        plans,
        userUuid,
      },
    });
  } catch (error) {
    console.error("Error loading subscription page:", error);
    return json({ success: false, error: "Failed to load subscription data" }, { status: 500 });
  }
}

export async function action({ request, context }: ActionFunctionArgs) {
  try {
    const db = context.db || createDbFromEnv(context.cloudflare?.env);

    const userUuid = await getUserUuid();
    if (!userUuid) {
      return json({ success: false, error: "Not authenticated" }, { status: 401 });
    }

    const formData = await request.formData();
    const action = formData.get("action") as string;
    const stripeSecretKey = context.cloudflare?.env?.STRIPE_SECRET_KEY;
    const webUrl = context.cloudflare?.env?.WEB_URL || "http://localhost:3000";

    if (!stripeSecretKey) {
      return json({ success: false, error: "Stripe not configured" });
    }

    switch (action) {
      case "subscribe": {
        const planId = formData.get("planId") as string;
        const result = await createSubscriptionCheckout(
          userUuid,
          planId,
          db,
          stripeSecretKey,
          webUrl
        );

        if (result.success && result.sessionId) {
          return json({
            success: true,
            redirect: `/api/stripe-redirect?session_id=${result.sessionId}`,
          });
        }

        return json({ success: false, error: result.error });
      }

      case "cancel": {
        const cancelAtPeriodEnd = formData.get("cancelAtPeriodEnd") === "true";
        const result = await cancelSubscription(userUuid, cancelAtPeriodEnd, db, stripeSecretKey);

        return json({
          success: result.success,
          message: result.success
            ? cancelAtPeriodEnd
              ? "Subscription will be canceled at the end of the current period"
              : "Subscription canceled successfully"
            : result.error,
        });
      }

      case "reactivate": {
        const result = await reactivateSubscription(userUuid, db, stripeSecretKey);

        return json({
          success: result.success,
          message: result.success ? "Subscription reactivated successfully" : result.error,
        });
      }

      case "calculate-proration": {
        const newPlanId = formData.get("newPlanId") as string;
        const result = await calculateProration(userUuid, newPlanId, db, stripeSecretKey);

        return json({
          success: result.success,
          data: { prorationAmount: result.amount },
          error: result.error,
        });
      }

      default:
        return json({ success: false, error: "Invalid action" });
    }
  } catch (error) {
    console.error("Error processing subscription action:", error);
    return json({ success: false, error: "Failed to process request" }, { status: 500 });
  }
}

export default function SubscriptionPage() {
  const { data } = useLoaderData<typeof loader>();
  const actionData = useActionData<typeof action>();
  const navigation = useNavigation();
  const [showCancelConfirm, setShowCancelConfirm] = useState(false);

  const isSubmitting = navigation.state === "submitting";
  const { subscription, plans } = data;

  const formatPrice = (price: number, currency: string) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency.toUpperCase(),
    }).format(price / 100);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "active":
        return "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400";
      case "canceled":
        return "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400";
      case "past_due":
        return "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400";
      default:
        return "bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "active":
        return <CheckCircle className="w-4 h-4" />;
      case "canceled":
        return <XCircle className="w-4 h-4" />;
      case "past_due":
        return <AlertTriangle className="w-4 h-4" />;
      default:
        return <RefreshCw className="w-4 h-4" />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            Subscription Management
          </h1>
          <p className="mt-2 text-gray-600 dark:text-gray-400">
            Manage your subscription plan and billing preferences
          </p>
        </div>

        {/* Action Messages */}
        {actionData && (
          <div
            className={`mb-6 p-4 rounded-lg flex items-center space-x-2 ${
              actionData.success
                ? "bg-green-50 text-green-800 border border-green-200 dark:bg-green-900/20 dark:text-green-400 dark:border-green-800"
                : "bg-red-50 text-red-800 border border-red-200 dark:bg-red-900/20 dark:text-red-400 dark:border-red-800"
            }`}
          >
            {actionData.success ? (
              <CheckCircle className="w-5 h-5" />
            ) : (
              <XCircle className="w-5 h-5" />
            )}
            <span>{actionData.message || actionData.error}</span>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Current Subscription */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center space-x-2">
                  <CreditCard className="w-5 h-5" />
                  <span>Current Subscription</span>
                </CardTitle>
              </CardHeader>
              <CardContent>
                {subscription ? (
                  <div className="space-y-6">
                    {/* Plan Details */}
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-lg font-semibold flex items-center space-x-2">
                          <Crown className="w-5 h-5 text-yellow-500" />
                          <span>{subscription.plan.name}</span>
                        </h3>
                        <p className="text-gray-600 dark:text-gray-400">
                          {subscription.plan.description}
                        </p>
                      </div>
                      <Badge className={getStatusColor(subscription.status)}>
                        <div className="flex items-center space-x-1">
                          {getStatusIcon(subscription.status)}
                          <span className="capitalize">{subscription.status}</span>
                        </div>
                      </Badge>
                    </div>

                    <Separator />

                    {/* Billing Information */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                          Monthly Price
                        </label>
                        <div className="text-2xl font-bold">
                          {formatPrice(subscription.plan.price, subscription.currency)}
                          <span className="text-sm font-normal text-gray-500">
                            /{subscription.plan.interval}
                          </span>
                        </div>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                          Credits Included
                        </label>
                        <div className="text-2xl font-bold flex items-center space-x-2">
                          <Zap className="w-5 h-5 text-yellow-500" />
                          <span>{subscription.plan.credits.toLocaleString()}</span>
                        </div>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                          Current Period
                        </label>
                        <div className="text-sm">
                          {new Date(subscription.periodStartsAt).toLocaleDateString()} -{" "}
                          {new Date(subscription.periodEndsAt).toLocaleDateString()}
                        </div>
                      </div>
                      <div>
                        <label className="text-sm font-medium text-gray-600 dark:text-gray-400">
                          Next Billing Date
                        </label>
                        <div className="text-sm flex items-center space-x-2">
                          <Calendar className="w-4 h-4" />
                          <span>{new Date(subscription.periodEndsAt).toLocaleDateString()}</span>
                        </div>
                      </div>
                    </div>

                    <Separator />

                    {/* Plan Features */}
                    <div>
                      <h4 className="font-semibold mb-3">Plan Features</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                        {subscription.plan.features.map((feature, index) => (
                          <div key={index} className="flex items-center space-x-2 text-sm">
                            <CheckCircle className="w-4 h-4 text-green-500" />
                            <span>{feature}</span>
                          </div>
                        ))}
                      </div>
                    </div>

                    <Separator />

                    {/* Actions */}
                    <div className="flex flex-wrap gap-3">
                      {subscription.cancelAtPeriodEnd ? (
                        <Form method="post">
                          <input type="hidden" name="action" value="reactivate" />
                          <Button type="submit" disabled={isSubmitting}>
                            <RefreshCw className="w-4 h-4 mr-2" />
                            Reactivate Subscription
                          </Button>
                        </Form>
                      ) : (
                        <>
                          <Button variant="outline" asChild>
                            <Link to="#plans" className="flex items-center space-x-2">
                              <TrendingUp className="w-4 h-4" />
                              <span>Change Plan</span>
                            </Link>
                          </Button>

                          {!showCancelConfirm ? (
                            <Button
                              variant="destructive"
                              onClick={() => setShowCancelConfirm(true)}
                            >
                              Cancel Subscription
                            </Button>
                          ) : (
                            <div className="flex space-x-2">
                              <Form method="post" className="inline">
                                <input type="hidden" name="action" value="cancel" />
                                <input type="hidden" name="cancelAtPeriodEnd" value="true" />
                                <Button type="submit" variant="destructive" size="sm">
                                  Cancel at Period End
                                </Button>
                              </Form>
                              <Button
                                variant="outline"
                                size="sm"
                                onClick={() => setShowCancelConfirm(false)}
                              >
                                Keep Subscription
                              </Button>
                            </div>
                          )}
                        </>
                      )}
                    </div>

                    {subscription.cancelAtPeriodEnd && (
                      <div className="p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                        <div className="flex items-center space-x-2">
                          <AlertTriangle className="w-5 h-5 text-yellow-600" />
                          <span className="font-semibold text-yellow-800 dark:text-yellow-400">
                            Subscription Ending
                          </span>
                        </div>
                        <p className="text-sm text-yellow-700 dark:text-yellow-300 mt-1">
                          Your subscription will end on{" "}
                          {new Date(subscription.periodEndsAt).toLocaleDateString()}. You can
                          reactivate it anytime before then.
                        </p>
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Crown className="w-12 h-12 mx-auto mb-4 text-gray-400" />
                    <h3 className="text-lg font-semibold mb-2">No Active Subscription</h3>
                    <p className="text-gray-600 dark:text-gray-400 mb-4">
                      You're currently on the free plan. Upgrade to unlock more features and
                      credits.
                    </p>
                    <Button asChild>
                      <Link to="#plans">Choose a Plan</Link>
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          {/* Quick Actions */}
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <Button asChild variant="outline" className="w-full">
                  <Link
                    to="/console/credits"
                    className="flex items-center justify-center space-x-2"
                  >
                    <CreditCard className="w-4 h-4" />
                    <span>View Credits</span>
                  </Link>
                </Button>

                <Button asChild variant="outline" className="w-full">
                  <Link to="/console/orders" className="flex items-center justify-center space-x-2">
                    <Calendar className="w-4 h-4" />
                    <span>Billing History</span>
                  </Link>
                </Button>

                <Button asChild variant="outline" className="w-full">
                  <Link to="/console/usage" className="flex items-center justify-center space-x-2">
                    <TrendingUp className="w-4 h-4" />
                    <span>Usage Analytics</span>
                  </Link>
                </Button>
              </CardContent>
            </Card>

            {subscription && (
              <Card>
                <CardHeader>
                  <CardTitle>Billing Summary</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600 dark:text-gray-400">Current Plan</span>
                      <span className="font-medium">{subscription.plan.name}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600 dark:text-gray-400">Monthly Cost</span>
                      <span className="font-medium">
                        {formatPrice(subscription.plan.price, subscription.currency)}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm text-gray-600 dark:text-gray-400">
                        Credits/Month
                      </span>
                      <span className="font-medium">
                        {subscription.plan.credits.toLocaleString()}
                      </span>
                    </div>
                    <Separator />
                    <div className="flex justify-between font-semibold">
                      <span>Next Payment</span>
                      <span>{new Date(subscription.periodEndsAt).toLocaleDateString()}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}
          </div>
        </div>

        {/* Available Plans */}
        <div id="plans" className="mt-12">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
            {subscription ? "Change Your Plan" : "Choose a Plan"}
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {plans.map((plan) => (
              <Card
                key={plan.id}
                className={`relative ${
                  subscription?.plan.id === plan.id ? "ring-2 ring-blue-500 border-blue-500" : ""
                }`}
              >
                {subscription?.plan.id === plan.id && (
                  <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                    <Badge className="bg-blue-500 text-white">Current Plan</Badge>
                  </div>
                )}

                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span>{plan.name}</span>
                    {plan.id.includes("yearly") && <Badge variant="secondary">Save 20%</Badge>}
                  </CardTitle>
                  <CardDescription>{plan.description}</CardDescription>
                  <div className="text-3xl font-bold">
                    {formatPrice(plan.price, plan.currency)}
                    <span className="text-sm font-normal text-gray-500">/{plan.interval}</span>
                  </div>
                </CardHeader>

                <CardContent>
                  <div className="space-y-4">
                    <div className="flex items-center space-x-2">
                      <Zap className="w-4 h-4 text-yellow-500" />
                      <span className="font-semibold">{plan.credits.toLocaleString()} credits</span>
                    </div>

                    <ul className="space-y-2">
                      {plan.features.map((feature, index) => (
                        <li key={index} className="flex items-center space-x-2 text-sm">
                          <CheckCircle className="w-4 h-4 text-green-500" />
                          <span>{feature}</span>
                        </li>
                      ))}
                    </ul>

                    {plan.id === "starter" ? (
                      <Button variant="outline" className="w-full" disabled>
                        Free Plan
                      </Button>
                    ) : subscription?.plan.id === plan.id ? (
                      <Button variant="outline" className="w-full" disabled>
                        Current Plan
                      </Button>
                    ) : (
                      <Form method="post">
                        <input type="hidden" name="action" value="subscribe" />
                        <input type="hidden" name="planId" value={plan.id} />
                        <Button type="submit" className="w-full" disabled={isSubmitting}>
                          {subscription ? (
                            plan.price > subscription.plan.price ? (
                              <>
                                <ArrowUpRight className="w-4 h-4 mr-2" />
                                Upgrade to {plan.name}
                              </>
                            ) : (
                              <>
                                <ArrowDownRight className="w-4 h-4 mr-2" />
                                Downgrade to {plan.name}
                              </>
                            )
                          ) : (
                            `Subscribe to ${plan.name}`
                          )}
                        </Button>
                      </Form>
                    )}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}
