# Project Rules

## Package Management
- Use **pnpm** as the package manager for all dependency management (specified in package.json as "packageManager": "pnpm@9.0.0")
- Always use package manager commands instead of manually editing package.json
- Run `pnpm install` for installing dependencies
- Use `pnpm add <package>` for adding new dependencies
- Use `pnpm remove <package>` for removing dependencies
- Use `pnpm run <script>` for running package scripts

## Code Standards
- All code, comments, and documentation must be written in **English**
- Use consistent naming conventions (camelCase for variables/functions, PascalCase for components)
- Follow TypeScript best practices and maintain strict type safety
- Use meaningful variable and function names that clearly describe their purpose
- Use **Biome** for linting and formatting (configured in biome.json)
  - Run `pnpm lint` for linting checks
  - Run `pnpm format` for code formatting
  - Run `pnpm check` for comprehensive code analysis
- **File naming**: Use descriptive names that reflect the file's primary purpose
  - Context files: `feature-context.tsx` (e.g., `google-auth.tsx`, `theme-context.tsx`)
  - Hook files: `useFeatureName.tsx` (e.g., `useOneTapLogin.tsx`)
  - Component files: `ComponentName.tsx` or `feature-component.tsx`

## Remix Route Conventions
- Use `.server.ts` or `.server.tsx` suffix for server-only routes (API endpoints, loaders without UI)
- This prevents client-side bundling and reduces bundle size
- All API routes should follow pattern: `app/routes/api.*.server.ts`
- Server-only auth routes: `app/routes/auth.*.server.tsx`
- Pure backend routes like sitemaps: `app/routes/sitemap[.]xml.server.tsx`

## Error Handling
- Handle errors gracefully instead of throwing direct errors when environment variables or dependencies are missing
- Log runtime errors (undefined destructuring, missing references) instead of crashing the application
- Provide fallback mechanisms for missing configurations

## State Management
- Use **Zustand** as the primary state management solution
- Eliminate other state management approaches in favor of unified Zustand-based patterns
- Keep state management simple and predictable

## UI/UX Guidelines
- Prefer subtle animations over excessive ones
- Ensure pricing sections are visually appealing and well-formatted
- Focus on clean, professional design patterns
- Use consistent spacing and typography
- Use **shadcn/ui** components as the primary UI library
- Regenerate problematic components using `npx shadcn add <component>` when encountering issues
- Maintain component consistency across the application

## Analytics
- Use **Google Analytics only** - avoid multiple analytics providers
- Keep analytics implementation simple and focused

## Git Workflow
- Write commit messages in English
- Create multiple smaller commits organized by functionality rather than single large commits
- Use descriptive commit messages that explain the "what" and "why"

## CI/CD
- Configure workflows to show warnings for code and format check failures rather than failing the entire pipeline
- Allow compilation and other steps to continue even when non-critical checks fail
- Maintain build stability while providing useful feedback

## Build Optimization
- Follow Remix conventions to prevent empty chunks in client bundles
- Server-only code must use `.server.ts/.server.tsx` extensions
- Use `pnpm run build` to verify build output
- Use `pnpm run analyze` to analyze bundle composition
- Keep vendor chunks under 500KB when possible
- Utilize manual chunk splitting in vite.config.ts for better caching

## Architecture Patterns
- Reference existing Next.js patterns (particularly from shipany-ai-saas project) when implementing new features
- Adapt API structures appropriately when migrating patterns from Next.js to Remix framework
- Maintain consistency with established project patterns
- Use Cloudflare Workers compatible patterns for server-side code
- Follow Remix conventions for data loading (loaders) and mutations (actions)

## Development Workflow
- Write tests for new functionality and run them to verify correctness
- Use the codebase-retrieval tool to understand existing code before making changes
- Be conservative with changes and respect existing codebase structure
- Use `pnpm run clean` to remove build artifacts when needed
- Run `pnpm run typecheck` to verify TypeScript correctness
- Use `pnpm run dev` for development server
- Deploy using `pnpm run deploy` (Wrangler)