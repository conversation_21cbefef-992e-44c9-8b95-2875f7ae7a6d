/**
 * API Route: Notifications Management
 * Handles notification CRUD operations and real-time updates
 */

import type { ActionFunctionArgs, LoaderFunctionArgs } from "@remix-run/cloudflare";
import { json } from "@remix-run/cloudflare";
import { respData, respErr } from "~/lib/api/resp";
import { createDbFromEnv } from "~/lib/db";
import {
  type CreateNotificationParams,
  createNotification,
  dismissNotification,
  getUnreadNotificationCount,
  getUserNotifications,
  markAllNotificationsAsRead,
  markNotificationAsRead,
  type NotificationType,
} from "~/services/notification.server";
import { getUserUuid } from "~/services/user-management.server";

export async function loader({ request, context }: LoaderFunctionArgs) {
  try {
    const db = context.db || createDbFromEnv(context.cloudflare?.env);

    const userUuid = await getUserUuid();
    if (!userUuid) {
      return respErr("Authentication required");
    }

    const url = new URL(request.url);
    const action = url.searchParams.get("action");

    switch (action) {
      case "unread-count": {
        const count = await getUnreadNotificationCount(userUuid, db);
        return respData({ unreadCount: count });
      }

      case "list": {
        const page = parseInt(url.searchParams.get("page") || "1", 10);
        const limit = parseInt(url.searchParams.get("limit") || "20", 10);
        const unreadOnly = url.searchParams.get("unread") === "true";
        const type = url.searchParams.get("type") as NotificationType | undefined;

        const result = await getUserNotifications(userUuid, { page, limit, unreadOnly, type }, db);
        return respData(result);
      }

      default:
        return respErr("Invalid action");
    }
  } catch (error) {
    console.error("Error in notifications API:", error);
    return respErr(error instanceof Error ? error.message : "Failed to process request");
  }
}

export async function action({ request, context }: ActionFunctionArgs) {
  try {
    const db = context.db || createDbFromEnv(context.cloudflare?.env);

    const userUuid = await getUserUuid();
    if (!userUuid) {
      return respErr("Authentication required");
    }

    const body = await request.json();
    const { action } = body;

    switch (action) {
      case "create": {
        const {
          title,
          body: notificationBody,
          type,
          channel,
          link,
          expiresAt,
          emailTemplate,
          emailVariables,
        } = body as CreateNotificationParams;

        if (!title || !notificationBody || !type || !channel) {
          return respErr("Missing required fields: title, body, type, channel");
        }

        const result = await createNotification(
          {
            accountId: userUuid,
            title,
            body: notificationBody,
            type,
            channel,
            link,
            expiresAt: expiresAt ? new Date(expiresAt) : undefined,
            emailTemplate,
            emailVariables,
          },
          db
        );

        if (result.success) {
          return respData({
            message: "Notification created successfully",
            notificationId: result.notificationId,
          });
        } else {
          return respErr(result.error || "Failed to create notification");
        }
      }

      case "mark-read": {
        const { notificationId } = body;

        if (!notificationId) {
          return respErr("Missing notificationId");
        }

        const result = await markNotificationAsRead(notificationId, userUuid, db);

        if (result.success) {
          return respData({ message: "Notification marked as read" });
        } else {
          return respErr(result.error || "Failed to mark notification as read");
        }
      }

      case "dismiss": {
        const { notificationId } = body;

        if (!notificationId) {
          return respErr("Missing notificationId");
        }

        const result = await dismissNotification(notificationId, userUuid, db);

        if (result.success) {
          return respData({ message: "Notification dismissed" });
        } else {
          return respErr(result.error || "Failed to dismiss notification");
        }
      }

      case "mark-all-read": {
        const result = await markAllNotificationsAsRead(userUuid, db);

        if (result.success) {
          return respData({ message: "All notifications marked as read" });
        } else {
          return respErr(result.error || "Failed to mark all notifications as read");
        }
      }

      default:
        return respErr("Invalid action");
    }
  } catch (error) {
    console.error("Error in notifications API action:", error);
    return respErr(error instanceof Error ? error.message : "Failed to process request");
  }
}
