// app/root.tsx
import type { LinksFunction, LoaderFunctionArgs } from "@remix-run/cloudflare";
import {
  Links,
  Meta,
  Outlet,
  Scripts,
  ScrollRestoration,
  useLoaderData,
  useLocation,
} from "@remix-run/react";
import { useEffect } from "react";
import { Toaster } from "sonner";
// import AnalyticsProvider from "~/components/analytics"; // Disabled for debugging

import { ZustandProvider } from "~/components/zustand-provider";
// Removed i18n-related imports: generateHreflangLinksForLayout, generateCanonicalLink, detectLanguageFromRequest
import { siteConfig } from "~/config/seo";
import { AppContextProvider } from "~/contexts/google-auth";
// import { pageview } from "~/lib/monitoring/analytics"; // Disabled for Cloudflare Workers compatibility
// import { initPerformanceMonitoring } from "~/lib/monitoring/performance"; // Disabled for Cloudflare Workers compatibility
import { useUIStore } from "~/stores/uiStore";

import "./tailwind.css";
import "~/styles/theme.css";
import "~/styles/animations.css";

export const links: LinksFunction = () => [
  { rel: "preconnect", href: "https://fonts.googleapis.com" },
  {
    rel: "preconnect",
    href: "https://fonts.gstatic.com",
    crossOrigin: "anonymous",
  },
  {
    rel: "stylesheet",
    href: "https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap",
  },
];

export async function loader({ context, request }: LoaderFunctionArgs) {
  try {
    const GA_TRACKING_ID = context?.cloudflare?.env?.GA_TRACKING_ID;
    if (!GA_TRACKING_ID) {
      console.warn("GA_TRACKING_ID is not configured in server environment.");
    }

    // Google One Tap ENV vars
    const GOOGLE_CLIENT_ID = context?.cloudflare?.env?.GOOGLE_CLIENT_ID;
    const ONE_TAP_ENABLED = context?.cloudflare?.env?.ONE_TAP_ENABLED === "true";

    // Neon Auth ENV vars
    const NEON_AUTH_ENABLED = context?.cloudflare?.env?.NEON_AUTH_ENABLED === "true";
    const VITE_STACK_PROJECT_ID = context?.cloudflare?.env?.VITE_STACK_PROJECT_ID;
    const VITE_STACK_PUBLISHABLE_CLIENT_KEY =
      context?.cloudflare?.env?.VITE_STACK_PUBLISHABLE_CLIENT_KEY;

    if (!GOOGLE_CLIENT_ID && ONE_TAP_ENABLED) {
      console.warn(
        "ONE_TAP_ENABLED is true, but GOOGLE_CLIENT_ID is not configured in server environment."
      );
    }

    if (NEON_AUTH_ENABLED && (!VITE_STACK_PROJECT_ID || !VITE_STACK_PUBLISHABLE_CLIENT_KEY)) {
      console.warn("NEON_AUTH_ENABLED is true, but Neon Auth configuration is incomplete.");
    }

    const url = new URL(request.url);

    console.log(`Loader - URL: ${url.href}`);

    return Response.json({
      GA_TRACKING_ID: GA_TRACKING_ID,
      ENV: {
        // Google One Tap vars
        GOOGLE_CLIENT_ID,
        ONE_TAP_ENABLED,
        // Neon Auth vars
        NEON_AUTH_ENABLED,
        VITE_STACK_PROJECT_ID,
        VITE_STACK_PUBLISHABLE_CLIENT_KEY,
      },
      pathname: url.pathname,
      siteConfig: {
        name: siteConfig.name,
        url: siteConfig.url,
      },
    });
  } catch (error) {
    console.error("Error in root loader:", error);
    // Return safe defaults instead of throwing
    return Response.json({
      GA_TRACKING_ID: undefined,
      ENV: {
        GOOGLE_CLIENT_ID: undefined,
        ONE_TAP_ENABLED: false,
        NEON_AUTH_ENABLED: false,
        VITE_STACK_PROJECT_ID: undefined,
        VITE_STACK_PUBLISHABLE_CLIENT_KEY: undefined,
      },
      pathname: "/",
      siteConfig: {
        name: siteConfig.name,
        url: siteConfig.url,
      },
    });
  }
}

export function Layout({ children }: { children: React.ReactNode }) {
  const location = useLocation();

  // Use useLoaderData with proper error handling for hydration
  const loaderData = useLoaderData<typeof loader>() as any;

  // Safe destructuring with fallback values to prevent crashes
  const GA_TRACKING_ID = loaderData?.GA_TRACKING_ID;
  const ENV = loaderData?.ENV || {
    GOOGLE_CLIENT_ID: undefined,
    ONE_TAP_ENABLED: false,
    NEON_AUTH_ENABLED: false,
    VITE_STACK_PROJECT_ID: undefined,
    VITE_STACK_PUBLISHABLE_CLIENT_KEY: undefined,
  };
  const pathname = loaderData?.pathname || "/";
  const siteConfigData = loaderData?.siteConfig || {};

  useEffect(() => {
    try {
      if (GA_TRACKING_ID && typeof window !== "undefined") {
        window.GA_TRACKING_ID = GA_TRACKING_ID;
        // Initialize performance monitoring
        // initPerformanceMonitoring(); // Disabled for Cloudflare Workers compatibility
      }
    } catch (error) {
      console.warn("Failed to set GA_TRACKING_ID on window:", error);
    }
  }, [GA_TRACKING_ID]);

  useEffect(() => {
    try {
      if (GA_TRACKING_ID) {
        // pageview(location.pathname + location.search); // Disabled for Cloudflare Workers compatibility
      }
    } catch (error) {
      console.warn("Failed to track pageview:", error);
    }
  }, [location, GA_TRACKING_ID]);

  // Removed useEffect for setting i18nextLng in localStorage and document.documentElement.lang

  // Hydrate Zustand stores
  useEffect(() => {
    if (typeof window !== "undefined") {
      // Trigger hydration for persisted stores
      // useUIStore.persist.rehydrate(); // Disabled due to global scope issues
    }
  }, []);

  // Removed hreflangLinks and canonicalLink generation
  const canonicalLinkHref = `${siteConfigData.url || ""}${pathname}`;

  return (
    <html lang="en">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />

        {/* SEO Meta Tags */}
        <Meta />

        {/* Canonical URL - simplified */}
        <link rel="canonical" href={canonicalLinkHref} />

        {/* Preconnect for performance */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />

        <Links />

        {/* Enhanced Analytics - Disabled for debugging */}
        {/* <AnalyticsProvider trackingId={GA_TRACKING_ID} /> */}

        {/* Inject environment variables for client-side access */}
        <script
          dangerouslySetInnerHTML={{
            __html: `
              window.__NEON_AUTH_ENABLED__ = ${JSON.stringify(ENV?.NEON_AUTH_ENABLED || false)};
              window.__NEON_AUTH_PROJECT_ID__ = ${JSON.stringify(ENV?.VITE_STACK_PROJECT_ID || "")};
              window.__NEON_AUTH_PUBLISHABLE_KEY__ = ${JSON.stringify(ENV?.VITE_STACK_PUBLISHABLE_CLIENT_KEY || "")};
            `,
          }}
        />
      </head>
      <body>
        <ZustandProvider>
          {/* Update AppContextProvider to pass props */}
          <AppContextProvider
            googleClientId={ENV?.GOOGLE_CLIENT_ID}
            oneTapEnabledEnv={ENV?.ONE_TAP_ENABLED}
            // promptParentId="your-prompt-parent-id" // Optional: Example if you have a specific element ID
          >
            {children}
            {/* Replace undefined Notifications component with Toaster from sonner */}
            <Toaster />
          </AppContextProvider>
        </ZustandProvider>
        <ScrollRestoration />
        <Scripts />
      </body>
    </html>
  );
}

// Error boundary component to handle errors gracefully
export function ErrorBoundary({ error }: { error: Error }) {
  console.error("Root error boundary caught an error:", error);

  // Don't show error boundary for Chrome DevTools requests
  if (error?.message?.includes?.("/.well-known/appspecific/com.chrome.devtools.json")) {
    return null;
  }

  return (
    <html lang="en">
      <head>
        <meta charSet="utf-8" />
        <meta name="viewport" content="width=device-width, initial-scale=1" />
        <title>Something went wrong</title>
        <Meta />
        <Links />
      </head>
      <body>
        <div className="min-h-screen flex items-center justify-center bg-gray-50">
          <div className="max-w-md w-full bg-white shadow-lg rounded-lg p-6">
            <div className="text-center">
              <h1 className="text-2xl font-bold text-gray-900 mb-4">Oops! Something went wrong</h1>
              <p className="text-gray-600 mb-6">
                We encountered an unexpected error. Please try refreshing the page.
              </p>
              <div className="text-xs text-gray-400 mb-4">
                Error: {error?.message || "Unknown error"}
              </div>
              <button
                onClick={() => window.location.reload()}
                className="bg-blue-500 hover:bg-blue-700 text-white font-bold py-2 px-4 rounded"
              >
                Refresh Page
              </button>
            </div>
          </div>
        </div>
        <Scripts />
      </body>
    </html>
  );
}

export default function App() {
  return <Outlet />;
}
