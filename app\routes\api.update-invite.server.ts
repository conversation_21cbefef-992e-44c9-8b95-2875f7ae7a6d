import type { ActionFunctionArgs } from "@remix-run/cloudflare";
import { respData, respErr } from "~/lib/api/resp";
import { getIsoTimestr } from "~/lib/utils/time";
import { findUserByInviteCode, findUserByUuid, updateUserInvitedBy } from "~/models/user";
import {
  CreditsAmount,
  CreditsTransType,
  getUserUuid,
  increaseCredits,
} from "~/services/user-management.server";

// Mock affiliate system constants
const AffiliateRewardAmount = 50;
const AffiliateRewardPercent = 10;
const AffiliateStatus = {
  ACTIVE: "active",
  PENDING: "pending",
};

// Mock affiliate model
async function insertAffiliate(affiliate: any) {
  console.log("Inserting affiliate:", affiliate);
  return affiliate;
}

export async function action({ request, context }: ActionFunctionArgs) {
  if (request.method !== "POST") {
    return respErr("Method not allowed");
  }

  try {
    const db = context.db;
    if (!db) {
      return respErr("Database not available");
    }

    const body = (await request.json()) as { invite_code?: string };
    const { invite_code } = body;

    if (!invite_code) {
      return respErr("invalid params");
    }

    const user_uuid = await getUserUuid();
    if (!user_uuid) {
      return respErr("no auth");
    }

    const user_info = await findUserByUuid(user_uuid, db);
    if (!user_info || !user_info.email) {
      return respErr("invalid user");
    }

    // Check if user already has an inviter
    if (user_info.invited_by) {
      return respErr("user already has an inviter");
    }

    // Find inviter by invite code
    const inviter = await findUserByInviteCode(invite_code, db);
    if (!inviter) {
      return respErr("invalid invite code");
    }

    // Can't invite yourself
    if (inviter.uuid === user_uuid) {
      return respErr("cannot invite yourself");
    }

    // Update user's invited_by field
    const success = await updateUserInvitedBy(user_uuid, inviter.uuid, db);
    if (!success) {
      return respErr("failed to update invite relationship");
    }

    // Give reward credits to the inviter
    await increaseCredits(
      {
        user_uuid: inviter.uuid,
        trans_type: CreditsTransType.Reward,
        credits: AffiliateRewardAmount,
        description: `Referral reward for inviting ${user_info.email}`,
      },
      db
    );

    // Create affiliate record
    const affiliate = {
      inviter_uuid: inviter.uuid,
      invitee_uuid: user_uuid,
      invitee_email: user_info.email,
      reward_amount: AffiliateRewardAmount,
      reward_percent: AffiliateRewardPercent,
      status: AffiliateStatus.ACTIVE,
      created_at: getIsoTimestr(),
    };

    await insertAffiliate(affiliate);

    // Return updated user info
    const updatedUser = await findUserByUuid(user_uuid, db);
    return respData({
      user: updatedUser,
      inviter: {
        name: inviter.name,
        email: inviter.email,
      },
      reward_given: AffiliateRewardAmount,
    });
  } catch (e) {
    console.log("update invite failed", e);
    return respErr("update invite failed");
  }
}
