/**
 * Google 认证路由 - 极简版本
 * 在新架构中，这个路由主要用于兼容性，实际认证由 Cookie 处理
 */

import type { LoaderFunctionArgs } from "@remix-run/cloudflare";
import { redirect } from "@remix-run/cloudflare";

// 在新架构中，Google One Tap 直接设置 g_credential Cookie
// 这个路由主要用于重定向和兼容性
export async function loader({ request }: LoaderFunctionArgs) {
  // 重定向到登录页面
  return redirect("/auth/login");
}

// 如果有 POST 请求，也重定向到登录页面
export async function action() {
  return redirect("/auth/login");
}
