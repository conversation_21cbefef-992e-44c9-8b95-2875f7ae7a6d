// Site-wide configuration for links, metadata, and feature flags
// This centralizes all link management and makes it easy to enable/disable sections

export const siteConfig = {
  name: "Remix Starter",
  description: "Build amazing applications with Remix, Cloudflare, and Neon Database.",
  tagline: "Modern Full-Stack Platform",
  url: "https://remixstarter.com",

  // Feature flags for different sections
  features: {
    showResources: false, // Hide Resources section for now
    showBlog: true,
    showCareers: false, // Hide until we have actual job postings
    showStatus: false, // Hide until we set up status page
    showChangelog: false, // Hide until we have release process
  },

  // Social media links
  social: {
    github: "https://github.com/yourcompany",
    twitter: "https://twitter.com/yourcompany",
    linkedin: "https://linkedin.com/company/yourcompany",
    email: "mailto:<EMAIL>",
  },

  // Contact information
  contact: {
    email: "<EMAIL>",
    support: "<EMAIL>",
    phone: "(*************",
  },
} as const;

// Footer link configuration with type safety
export const footerLinks = [
  {
    title: "Product",
    items: [
      { title: "AI Tools", href: "/dev/ai-tools", internal: true },
      { title: "Components", href: "/dev/components", internal: true },
      { title: "Pricing", href: "/pricing", internal: true },
      { title: "Documentation", href: "/docs", internal: true },
    ],
  },
  {
    title: "Company",
    items: [
      { title: "About", href: "/about", internal: true },
      // Blog/Careers feature flags should be handled in page logic, not config
      { title: "Contact", href: "/contact", internal: true },
    ],
  },
  {
    title: "Legal",
    items: [
      { title: "Privacy Policy", href: "/legal/privacy", internal: true },
      { title: "Terms of Service", href: "/legal/terms", internal: true },
      { title: "Website Agreement", href: "/legal/agreement", internal: true },
    ],
  },
] as const;

// Type definitions derived from the config
export type FooterLinkSection = (typeof footerLinks)[number];
export type FooterLink = FooterLinkSection["items"][number];

// Helper function to check if a URL is external
export function isExternalUrl(url: string): boolean {
  return url.startsWith("http://") || url.startsWith("https://");
}

// Analytics tracking helper
export function getAnalyticsProps(section: string, link: string) {
  return {
    "data-analytics": "footer-link",
    "data-section": section.toLowerCase(),
    "data-link": link.toLowerCase().replace(/\s+/g, "-"),
  };
}
export type SocialPlatform = keyof typeof siteConfig.social;
